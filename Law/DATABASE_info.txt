User

This is your central entity for anyone interacting with the system. The userType field dictates what other fields are present in their profile and their accessControl.

JSON
{
  "userId": "unique_uuid_or_id",
  "email": "<EMAIL>",
  "passwordHash": "hashed_password_string",
  "userType": "Advocate" | "JrAdvocate" | "Intern", // Discriminator
  "languagePreference": "en" | "hi" | "kn", // For Regional Language Selector
  "createdAt": "YYYY-MM-DDTHH:MM:SSZ",
  "lastLogin": "YYYY-MM-DDTHH:MM:SSZ",
  "profile": { // Embedded sub-document, content depends on userType
    "name": {
      "firstName": "<PERSON>",
      "lastName": "Doe"
    },
    // Common fields
    "contact": {
      "email": "<EMAIL>", // Redundant with top-level, but good for embedding if needed
      "mobile": "+************",
      "whatsapp": "+************"
    },
    "address": { // Office Address for advocates, could be general for others
      "street": "123 Main St",
      "city": "Bengaluru",
      "state": "Karnataka",
      "zipCode": "560001"
    },

    // Advocate/Jr. Advocate Specific
    "advocateLicenseNumber": "ADV123456",
    "officeName": "Legal Eagles Law Firm",
    "numberOfEmployees": 5,
    "registeredCompanyType": "Partnership" | "Sole Proprietorship" | "LLP",
    "roleAtCompany": "Senior Partner" | "Junior Associate",
    "advocateStatus": "Active" | "Pending Approval",

    // Intern Specific
    "age": 20,
    "college": "National Law School of India University",
    "resumeUrl": "https://example.com/alice_resume.pdf",
    "licensesEnrolledStatus": "Student" | "Y" | "N",
    "internRequests": [ // Store intern's requests to advocates
      {
        "advocateId": "advocate_user_id_1",
        "status": "Pending" | "Accepted" | "Rejected",
        "requestedAt": "YYYY-MM-DDTHH:MM:SSZ",
        "acceptedAt": "YYYY-MM-DDTHH:MM:SSZ"
      }
    ]
  },
  "accessControl": { // Granular permissions for dashboard and features
    "canAddCases": true,
    "canRemoveCases": true,
    "canViewAnalytics": true,
    "canAccessClientDetails": true,
    "canShareAIResults": true,
    "canGrantInternAccess": true,
    "canAcceptInternRequests": true,
    "canAccessVoiceFeatures": true, // For Intern's voice feature
    "canAddNotesToHearings": true,
    "canManageJobSheets": true // Advocates assign, Interns view/update
  }
}





2. Case

This schema is comprehensive, embedding all case-specific details and related actions.

JSON
{
  "caseId": "unique_case_id",
  "caseType": "Civil" | "Criminal" | "Family" | "Corporate" | "Tax",
  "status": "Active" | "Closed" | "Pending Hearing" | "Archived",
  "filing": {
    "number": "FN-2023-001",
    "date": "YYYY-MM-DD"
  },
  "registration": {
    "number": "RN-2023-005" | "CN-2023-010",
    "date": "YYYY-MM-DD"
  },
  "crnNumber": "CRN-XYZ-123",
  "uniqueId": "UNIQUE-ID-456",
  "assignedUsers": [ // Array of userId references for advocates, jr. advocates, interns
    { "userId": "advocate_user_id_1", "role": "Advocate" },
    { "userId": "intern_user_id_1", "role": "Intern" }
  ],
  "createdAt": "YYYY-MM-DDTHH:MM:SSZ",
  "updatedAt": "YYYY-MM-DDTHH:MM:SSZ",

  "clientDetails": { // Embedded client details for direct access
    "name": "Client Name",
    "contact": {
      "email": "<EMAIL>",
      "phone": "+************"
    },
    "documents": [ // Embedded client documents
      {
        "documentId": "doc_uuid_1",
        "name": "Agreement Copy",
        "url": "https://storage.link/agreement.pdf",
        "uploadedByUserId": "user_id_who_uploaded",
        "uploadedAt": "YYYY-MM-DDTHH:MM:SSZ",
        "metadata": { "type": "pdf", "sizeKB": 500 } // Example metadata
      }
    ]
  },

  "caseResearchFindings": [ // Embedded research findings
    {
      "findingId": "research_uuid_1",
      "title": "Relevant Precedent 1",
      "summary": "Summary of research finding...",
      "sourceUrl": "https://law.example.com/precedent_1",
      "addedByUserId": "user_id_who_added",
      "addedAt": "YYYY-MM-DDTHH:MM:SSZ"
    }
  ],

  "hearings": [ // Embedded sub-documents for all hearings
    {
      "hearingId": "hearing_uuid_1",
      "date": "YYYY-MM-DD",
      "time": "HH:MM",
      "court": "High Court of Karnataka",
      "judge": "Justice A. Sharma",
      "status": "Adjourned" | "Heard" | "Next Date Given",
      "nextHearing": { // If applicable
        "date": "YYYY-MM-DD",
        "time": "HH:MM"
      },
      "notesAndComments": [ // Intern/Advocate comments during/after court hearing
        {
          "noteId": "note_uuid_1",
          "text": "Intern's observation: Judge focused on Section B.",
          "addedByUserId": "user_id_who_added",
          "addedAt": "YYYY-MM-DDTHH:MM:SSZ"
        }
      ]
    }
  ],

  "caseHistory": [ // Log of major changes and activities
    {
      "event": "Status Changed",
      "description": "Case status updated to 'Pending Hearing'",
      "changedByUserId": "advocate_user_id_1",
      "changedAt": "YYYY-MM-DDTHH:MM:SSZ"
    }
  ],
  "aiSummaries": [ // For SUMMARIZER/REPORT AI API CALL
    {
      "summaryId": "summary_uuid_1",
      "type": "Case Brief",
      "generatedAt": "YYYY-MM-DDTHH:MM:SSZ",
      "content": "AI generated summary of the case...",
      "sharedWithClient": false // To track "Share case information to clients"
    }
  ]
}


3. DiaryEntry

This schema handles both calendar events and daily notes, linking to cases where relevant.

JSON
{
  "entryId": "unique_entry_id",
  "userId": "user_id_who_created_it", // User who owns this diary entry
  "caseId": "case_id_if_related", // Optional reference to a Case
  "type": "Hearing" | "Meeting" | "Deadline" | "Task" | "Personal" | "Note", // Broad types
  "title": "Next Hearing for Case RN-2023-005",
  "description": "Prepare arguments for XYZ section.",
  "date": "YYYY-MM-DD",
  "time": "HH:MM",
  "location": "Court Room 5" | "Office" | null,
  "status": "Scheduled" | "Completed" | "Cancelled" | "Pending", // For tasks/events
  "createdAt": "YYYY-MM-DDTHH:MM:SSZ",
  "updatedAt": "YYYY-MM-DDTHH:MM:SSZ"
  // Note: Specific comments/notes for hearings are in the Case.hearings.notesAndComments
  // This is for general diary notes or event-specific notes.
}





4. JobSheet

Specifically for managing tasks assigned to interns.

JSON
{
  "jobSheetId": "unique_job_sheet_id",
  "internId": "intern_user_id_1", // Reference to the Intern User
  "assignedByAdvocateId": "advocate_user_id_1", // Reference to the Advocate User
  "caseId": "case_id_if_related", // Optional reference to a Case
  "title": "Research on Property Law",
  "description": "Find precedents related to shared property disputes.",
  "assignedDate": "YYYY-MM-DDTHH:MM:SSZ",
  "dueDate": "YYYY-MM-DD",
  "status": "Assigned" | "In Progress" | "Completed" | "Under Review",
  "deliverables": [ // List of deliverables or uploaded documents for the job sheet
    {
      "deliverableId": "del_uuid_1",
      "name": "Research Report",
      "url": "https://storage.link/intern_report.docx",
      "submittedAt": "YYYY-MM-DDTHH:MM:SSZ"
    }
  ],
  "feedback": "Good work, need to improve on citation.", // Advocate feedback
  "feedbackByAdvocateId": "advocate_user_id_1",
  "feedbackGivenAt": "YYYY-MM-DDTHH:MM:SSZ",
  "comments": [ // Communication between intern and advocate on the job sheet
    {
      "commentId": "comment_uuid_1",
      "text": "Clarification needed on section X.",
      "addedByUserId": "user_id",
      "addedAt": "YYYY-MM-DDTHH:MM:SSZ"
    }
  ],
  "createdAt": "YYYY-MM-DDTHH:MM:SSZ",
  "updatedAt": "YYYY-MM-DDTHH:MM:SSZ"
}




5. YellowPagesEntry

A general directory for legal professionals and services.

JSON
{
  "entryId": "unique_dir_entry_id",
  "name": "Advocate Ramesh Kumar",
  "category": "Advocate" | "Law Firm" | "Notary" | "Court Clerk" | "Expert Witness" | "Other" // users can add any text as category,
  "contactDetails": {
    "phone": "+************",
    "email": "<EMAIL>",
    "address": "101, Legal Chambers, City Name"
  },
  "specialties": ["Criminal Law", "Civil Law"], // For Advocates/Firms
  "website": "https://rameshkumar.com",
  "description": "Specialized in High Court litigation.",
  "rating": 4.5, // Optional
  "addedByUserId": "user_id_who_added_it", // Tracks who added the entry
  "createdAt": "YYYY-MM-DDTHH:MM:SSZ"
}


