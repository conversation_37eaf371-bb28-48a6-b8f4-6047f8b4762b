I. Global UX/UI Elements & Common Features
Regional Language Selector (Initial & Persistent):

Placement: Prominently displayed on the very first screen (splash/welcome), and then available via a subtle icon (e.g., a globe or dropdown) in the top-right corner of the navigation bar/header on all subsequent pages.
Initial Interaction: On first launch, a full-screen overlay or modal asking "Select your preferred language" with large, clear buttons for major regional languages (e.g., English, Hindi, Kannada, Tamil, Telugu, Marathi, Bengali, Gujarati). A "Save & Continue" button.
Persistent Access: Tapping the globe icon anywhere in the app brings up a small, quick-select modal or dropdown to change language on the fly. This changes all UI text and potentially content (if translated).
Persistence: The selected language is saved to user preferences (and local storage) and remembered for future sessions.
Notifications (Push & In-App):

In-App: A bell icon in the top navigation bar/header. A small red badge indicates unread notifications.
Content:
"Cases to attend today": Proactive reminders for scheduled hearings.
"New intern request from [Intern Name]" (for Advocates).
"Your request to [Advocate Name] has been [Accepted/Rejected]" (for Interns).
"New Job Sheet assigned: [Job Sheet Title]" (for Interns).
"Feedback on Job Sheet [Job Sheet Title] from [Advocate Name]" (for Interns).
"Case [Case Number] status updated to [New Status]" (for all assigned users).
"New document added to Case [Case Number]" (for all assigned users).
"AI Summary ready for Case [Case Number]" (for Advocates).
Interaction: Tapping a notification opens the relevant page (e.g., tapping "Case to attend today" opens the case details or calendar entry).
Global Search Feature:

Placement: Magnifying glass icon in the top header on most main screens (Dashboard, My Cases).
Functionality: Universal search across Cases (by number, name, client name), Diary Entries (by title, date), Job Sheets (by title, intern name), and Yellow Pages (by name, category).
Interaction: Tapping opens a search bar. As the user types, results appear dynamically below (e.g., "Case: RN-2023-005 (Civil)", "Advocate: John Doe (Yellow Pages)", "Hearing: Today - Case CR-2024-001").
Help and Customer Support (AI-powered):

Placement: Dedicated "Help" or "Support" option in the main menu (sidebar navigation or bottom tabs).
Functionality:
AI Chatbot: A chat interface where users can type questions. The chatbot (Gemini Text API) attempts to answer common FAQs, guide users through features, or provide information.
Escalation: Option to "Talk to a Human" or "Submit a Support Ticket" if the chatbot can't resolve the issue, providing pre-filled user details.
Knowledge Base: A searchable list of FAQ articles, tutorials, and guides, curated by admins.
Little Details:
Context-aware suggestions from the chatbot based on the page the user is on (e.g., if on "My Cases", suggest "How to add a new case?").
Ability to rate chatbot response ("Was this helpful?").
II. Workflow & Page-Specific UX/UI
1. Welcome & Login Page

Initial Screen:
App Logo and Name.
Regional Language Selector: Prominently displayed (e.g., "Choose Language" button or flag icons).
"Login" button.
"Create New Account" button.
Login Screen:
Email/Phone Number input field.
Password input field (with "Show/Hide" toggle).
"Forgot Password?" link.
"Login" button.
Social Logins: "Continue with Google" and "Continue with Outlook" buttons.
"Don't have an account? Sign Up." link.
2. Create Profile - Persona Selection

Screen: "What type of user are you?"
Clear cards/buttons for:
Sr. Advocate: Large, prominent.
Jr. Advocate: Large, prominent.
Intern: Large, prominent.
Brief description under each (e.g., "Senior Advocate: Full practice, manage cases & interns.").
"Back" button.
Workflow: User selects their persona, which leads to the appropriate profile creation form.
3. Profile Creation Forms

General Input: Standard form fields with clear labels, placeholders, and validation (e.g., "Email is required," "Invalid license number format").

Progress Indicator: A simple "Step 1 of 3" or a progress bar.

a. Advocate Profile (Sr./Jr. Advocate shares most fields):

Step 1: Basic Information
Full Name (First, Last Name)
Email (pre-filled if using social login)
Password (if not social login)
Contact Number (with country code selector, optional WhatsApp toggle)
Office Name
Office Address (Street, City, State, Pin Code - with autocomplete suggestions if possible)
Step 2: Professional Details
Advocate License Number (with format helper text)
Number of Employees (Dropdown/Number input, e.g., 1-5, 6-10, 10+)
Registered Company Type (Dropdown: Sole Proprietorship, Partnership, LLP, Private Limited, Not Applicable)
Role at Company (Text input: e.g., "Senior Partner", "Associate", "Proprietor")
Step 3: Review & Submit
Summary of entered details.
"Agree to Terms & Conditions" checkbox.
"Create Account" button.
Post-Submission: "Account created successfully! Awaiting verification/approval" (for Sr./Jr. Advocates if a manual approval process is needed).
b. Intern Profile:

Step 1: Basic Information
Full Name (First, Last Name)
Email (pre-filled)
Password (if not social login)
Contact Number (with country code selector)
Age (Number input/Date of Birth picker)
College Name (Text input, with suggestions if a pre-defined list)
Step 2: Professional & Resume
Licenses: "Currently enrolled in a Bar Council program?" (Yes/No/Student).
Upload Resume (Button: "Upload File" - accepts PDF/DOCX, shows file name after upload).
Step 3: Review & Submit
Summary.
"Agree to Terms & Conditions" checkbox.
"Create Account" button.
Post-Submission: "Account created! You can now request access from Advocates."
4. Intern Request to Advocate Workflow

Intern's Journey:
Discover Advocates: Intern goes to "Yellow Pages" or a dedicated "Find Advocates" section.
Advocate Profile View: Taps on an Advocate's listing to see their public profile (Name, Office, Contact, Specialties).
Request Button: A prominent "Request Access" button on the Advocate's profile page.
Request Form:
Pre-filled: Intern's Name, Email, Company Name (from profile).
Optional: Message field ("Why you want to intern with them").
"Send Request" button.
Confirmation: "Request sent to [Advocate Name]! You'll be notified when they respond."
Advocate's Journey:
Notification: Push/in-app notification: "New Intern Request from [Intern Name]".
Dashboard Widget: A section on the Advocate's dashboard showing "Pending Intern Requests."
Request Review: Tapping the notification or widget opens a modal/page:
Intern's Name, College, Resume Link (downloadable).
Optional message from Intern.
Buttons: "Accept Request" | "Reject Request".
Access Granting (upon Acceptance):
If "Accept Request" is tapped: A prompt "Grant access to which cases?"
A list of the Advocate's My Cases with checkboxes next to each.
"Select All" option.
"Confirm Access" button.
Little Detail: The system automatically adds the Intern's userId to the assignedUsers array of the selected Case documents.
III. Main Dashboards & Features
1. Advocate Home / Dashboard

Layout: Clean, organized dashboard with a clear header, notification icon, user profile access, and prominent widgets.
Top Bar: App Logo, User Profile Icon (Avatar/Name), Bell (Notifications), Search Icon.
Greeting: "Welcome, [Advocate Name]!"
Key Widgets/Sections (Prioritized):
"Cases to Attend Today" (Notification-driven): A card showing today's hearings from the user's DiaryEntry and Case.hearings. Tapping opens the specific case or diary entry.
"My Cases Overview":
Quick stats: "Total Cases: X", "Pending Hearings: Y", "Cases Closed: Z".
Small carousel or list of "Recent Active Cases" (Case Name, Status).
"Pending Intern Requests": (Only if applicable) Card showing number of pending requests. Tapping leads to the Intern Request review workflow.
"Quick Actions": Prominent buttons for:
"+ Add New Case"
"View All My Cases"
"Go to Diary"
"Access Yellow Pages"
"Analytics Snapshot": (Simplified view of "Employee Analytics")
"Your Performance": Graph of cases closed/month, average hearing attendance.
"Team Performance": (if numberOfEmployees > 1) Overview of cases handled by jr. advocates/interns.
Bottom Navigation (Four Boxes):
My Cases: Icon (briefcase), Label. Tapping navigates to "My Cases" page.
Yellow Pages: Icon (phone book/directory), Label. Tapping navigates to "Yellow Pages" page.
Share Case Information to Clients: Icon (share), Label. Tapping initiates a workflow to select a case and share its AI summary/documents.
Employee Analytics: Icon (graph/chart), Label. Tapping navigates to the detailed "Employee Analytics" page.
2. Intern Home / Dashboard

Layout: Similar to Advocate, but focused on Intern-specific tasks and information.

Top Bar: App Logo, User Profile Icon, Bell (Notifications), Search Icon.

Greeting: "Hello, [Intern Name]!"

Key Widgets/Sections:

"Cases to Attend Today": Same as Advocate, but only for cases they have access to.
"My Active Job Sheets":
Quick view of "Assigned: X", "In Progress: Y", "Due Soon: Z".
Small list of "Recent Job Sheets" (Title, Due Date, Status).
"Advocate Access Status": Shows list of advocates they've requested/been accepted by.
"Quick Actions":
"View All My Cases"
"Go to Diary"
"Access Yellow Pages"
"Voice Feature for Case Status":
Prominent Button: A large microphone icon/button labeled "Ask about Case Status" or "Voice Search".
Interaction: Tapping activates listening (shows waveform/mic animation). User speaks case number or client name.
AI VOICE FEATURE (Cause List): If user asks "What's the cause list for today?", Gemini Voice API processes, looks up DiaryEntry or Case.hearings for today, and reads out relevant entries.
Response: AI Voice (Gemini Voice API) speaks the status/details. Text transcription of response also displayed.
Little Details:
Clear feedback on listening (e.g., "Listening...", "Processing...").
Ability to interrupt/cancel.
Option to re-listen to the response.
Bottom Navigation (Four Boxes):

My Cases: Icon, Label. Tapping navigates to "My Cases" page.
Yellow Pages: Icon, Label. Tapping navigates to "Yellow Pages" page.
Share Case Information to Clients: This is typically for Advocates. For interns, this might be "Share Case Findings" or "Submit Research" instead, leading to a workflow to add research to a case. Revisit this based on Intern permissions. If Interns can share, it means sharing their research findings.
Intern Profile: Icon (person), Label. Tapping navigates to the Intern's own profile viewing/editing page.
IV. Shared Features & Detail Pages

1. My Cases Page (Advocate & Intern)

Layout: Tabbed interface or filter options at the top.
Tabs: "Active Cases", "Closed Cases", "Pending Hearings", "All Cases".
Search bar within this page for case-specific filtering.
Filter/Sort options (e.g., by Case Type, Filing Date, Advocate Name).
Case List: Each case displayed as a card/row.
Information: Case Type, Registration/Filing Number, Client Name, Current Status, Next Hearing Date (if any), Assigned Advocate(s)/Intern(s) (small icons).
Quick Actions (Swipe/Long Press):
"View Details"
"Add Hearing"
"Update Status"
"Remove Case" (with confirmation, requires accessControl.canRemoveCases)
Add New Case (Button): Prominent floating action button (FAB) or top-right button.
Form Workflow:
Step 1: Core Case Details:
Case Type (Dropdown)
Filing Number, Filing Date (Date picker)
Registration Number/Case Number, Registration Date (Date picker)
CRN Number, Unique ID (Text input)
Advocate Name (Dropdown of current user + Jr. Advocates in their firm).
Step 2: Client & Initial Docs:
Client Name, Contact (Email, Phone)
Upload Initial Documents (Multiple file upload, shows progress).
Step 3: Assign Users (Advocate only):
List of available Jr. Advocates/Interns with checkboxes to assign.
Submit/Save: "Create Case" button.
Case Details Page:
Header: Case Type, Registration/Filing Number.
Tabs/Sections:
Overview: Key case details (Client Name, Status, CRN, Unique ID, Assigned Users).
Hearings: List of all hearings.
Each hearing: Date, Time, Court, Judge, Status, Next Hearing.
Little Detail (Interns/Advocates): For past hearings, an "Add/View Notes" button. Tapping this opens a text editor where users can write comments and add notes (Case.hearings.notesAndComments). This editor could support rich text or simple markdown.
"+ Add New Hearing" button.
Documents: List of all documents (from clientDetails.documents and other general case documents).
"Upload Document" button.
"View Document" (opens PDF viewer/image viewer).
Research Findings: List of caseResearchFindings.
"+ Add Research Finding" button (Title, Summary, Source URL, Upload related files).
AI Summaries: List of aiSummaries.
"Generate AI Summary" Button (Advocates only): Triggers SUMMARIZER/REPORT AI API CALL. Shows "Generating..." state. Once done, adds a new entry to aiSummaries.
"Share AI PDF" Button (Advocates only): For each generated AI summary, a share icon. Tapping it opens standard sharing options (Email, WhatsApp, Download PDF).
Case History: Chronological log of significant events (status changes, document uploads, user assignments).
Remove Cases: Accessible from "My Cases" list (swipe/long press) or within "Case Details" page. Requires confirmation modal.
2. Diary / Calendar

Layout:
Calendar View: Standard month view with dots/highlights on dates with events.
List View (Agenda): Day/Week view showing events in chronological order.
Toggle between "Calendar" and "List" views.
Interaction:
Tapping a date in Calendar view switches to List view for that day.
Scrolling in List view loads more days/weeks.
"+ Add New Entry" (FAB or button).
Add New Entry Form:
Type (Dropdown: Hearing, Meeting, Deadline, Task, Personal Note).
Title, Description.
Date, Time.
Optional: Link to Case (Search/select from My Cases).
Location.
"Save" button.
Diary Details: Tapping an entry in the list view opens a modal/page:
Full details of the DiaryEntry.
"Edit" | "Delete" buttons.
3. Job Sheet for Interns (Intern View)

Page Title: "My Job Sheets"
Layout: List of job sheets, filterable by Status (Assigned, In Progress, Under Review, Completed).
Job Sheet Card/Row:
Title, Assigned By, Due Date, Status.
Case Link (if applicable).
Job Sheet Details Page:
Title, Description.
Assigned By: Advocate Name.
Assigned Date, Due Date.
Current Status (Dropdown: In Progress, Under Review, Completed - for Interns to update).
Deliverables:
"Upload Deliverable" button (e.g., research report, summary).
Feedback: Read-only section showing Advocate's feedback (feedback field).
Comments: Chat-like interface for communication (comments array).
Input field for new comments.
"Send" button.
4. Yellow Pages Format

Layout: Grid or List view of directory entries.
Search Bar: Filter by Name, Category, Specialty.
Filters: Buttons/Dropdowns for "Category" (Advocate, Law Firm, Notary, etc.).
Entry Card: Name, Category, Key contact (Phone/Email), Specialties.
Entry Details Page: Tapping an entry shows full YellowPagesEntry details.
Advocate Specific: For advocates, an option for interns to "Request Access" (leads to the intern request workflow).
Little Detail: Could include "Add New Entry" (if users are allowed to contribute, subject to admin approval).
5. Employee Analytics (Advocate Only)

Page Title: "Employee Analytics"
Layout: Dashboard-like view with charts and graphs.
Key Metrics:
Case Load Distribution: Bar chart showing number of active cases assigned per Jr. Advocate/Intern.
Case Status Breakdown: Pie chart of "Active," "Pending Hearing," "Closed" cases across the team.
Job Sheet Completion Rate: Graph showing percentage of job sheets completed on time by interns.
Hearing Attendance: Overview of attended vs. missed hearings by team members.
Individual Performance Cards: Small cards for each Jr. Advocate/Intern with quick stats (e.g., "Cases Handled: X", "Job Sheets Completed: Y").
Filters: Date range selector (e.g., Last Month, Last Quarter, Custom).
V. Integration Details
Gmail and Outlook Login:
Standard OAuth2 flow. User clicks "Continue with Google/Outlook". Redirected to Google/Outlook login page. After successful login, user grants permission to your app. Your app receives an access token.
User's email is pre-filled. passwordHash is not needed if only social login is used.
Google and Outlook Calendar Integration:
After user logs in, prompt: "Connect your Google/Outlook Calendar?"
If accepted, initiates OAuth2 flow for calendar access.
Functionality:
Sync Outbound: Automatically add DiaryEntry type "Hearing" or "Meeting" events to the user's connected external calendar.
Sync Inbound (Optional): Potentially pull events from the external calendar into the app's Diary for a unified view. This requires careful handling of duplicates.
One-Time Sync: When connecting, offer to import existing relevant calendar events.
Gemini Voice and Text API (Dummy API Integration):
Text API: Used for HELP and Customer Support AI (chatbot) and SUMMARIZER/REPORT AI API CALL.
Implementation: An internal service layer makes authenticated calls to the Gemini Text API. The prompts are constructed based on user input or case data.
Voice API: Used for AI VOICE FEATURE (Cause List) and Voice feature for case status.
Implementation: Client-side (mobile/web app) captures audio, sends to a speech-to-text API (could be a basic one or part of Gemini if available). The transcribed text is then sent to your backend, which uses Gemini Text API for processing, and then a text-to-speech API (could be part of Gemini or a separate service) to generate audio response.